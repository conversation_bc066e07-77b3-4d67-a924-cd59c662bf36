from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from models.database import get_db, User, Bet, Transaction
from models.schemas import (
    BetCreate, Bet as BetSchema, BetWithEvent,
    CashOutRequest, CashOutResponse,
    Transaction as TransactionSchema
)
from services.auth import get_current_active_user
from services.betting import BettingService

router = APIRouter(prefix="/betting", tags=["betting"])

@router.post("/place", response_model=BetSchema)
async def place_bet(
    bet_data: BetCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Place a new bet."""
    betting_service = BettingService()

    try:
        bet = betting_service.place_bet(db, current_user, bet_data)
        return bet
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to place bet")

@router.post("/cashout", response_model=CashOutResponse)
async def cash_out_bet(
    cashout_data: CashOutRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Cash out a bet before the event concludes."""
    betting_service = BettingService()

    try:
        result = betting_service.cash_out_bet(db, current_user, cashout_data.bet_id)
        return CashOutResponse(**result)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to cash out bet")

@router.get("/my-bets", response_model=List[BetWithEvent])
async def get_my_bets(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's bets."""
    betting_service = BettingService()

    # Convert string status to enum if provided
    bet_status = None
    if status:
        from models.database import BetStatus
        try:
            bet_status = BetStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid bet status")

    bets = betting_service.get_user_bets(db, current_user.id, bet_status)

    # Attach event information to each bet
    bets_with_events = []
    for bet in bets:
        from models.database import Event
        event = db.query(Event).filter(Event.id == bet.event_id).first()
        bet_dict = BetSchema.from_orm(bet).dict()
        bet_dict["event"] = event
        bets_with_events.append(bet_dict)

    return bets_with_events

@router.get("/history", response_model=List[BetWithEvent])
async def get_bet_history(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get betting history for current user."""
    betting_service = BettingService()
    bets = betting_service.get_bet_history(db, current_user.id, limit)

    # Attach event information to each bet
    bets_with_events = []
    for bet in bets:
        from models.database import Event
        event = db.query(Event).filter(Event.id == bet.event_id).first()
        bet_dict = BetSchema.from_orm(bet).dict()
        bet_dict["event"] = event
        bets_with_events.append(bet_dict)

    return bets_with_events

@router.get("/transactions", response_model=List[TransactionSchema])
async def get_transactions(
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get transaction history for current user."""
    transactions = db.query(Transaction)\
        .filter(Transaction.user_id == current_user.id)\
        .order_by(Transaction.created_at.desc())\
        .limit(limit).all()

    return transactions

@router.get("/stats")
async def get_betting_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get betting statistics for current user."""
    from sqlalchemy import func
    from models.database import BetStatus

    # Total bets
    total_bets = db.query(func.count(Bet.id))\
        .filter(Bet.user_id == current_user.id).scalar()

    # Active bets
    active_bets = db.query(func.count(Bet.id))\
        .filter(Bet.user_id == current_user.id)\
        .filter(Bet.status.in_([BetStatus.PENDING, BetStatus.MATCHED])).scalar()

    # Won bets
    won_bets = db.query(func.count(Bet.id))\
        .filter(Bet.user_id == current_user.id)\
        .filter(Bet.status == BetStatus.WON).scalar()

    # Lost bets
    lost_bets = db.query(func.count(Bet.id))\
        .filter(Bet.user_id == current_user.id)\
        .filter(Bet.status == BetStatus.LOST).scalar()

    # Total amount wagered
    total_wagered = db.query(func.sum(Bet.amount))\
        .filter(Bet.user_id == current_user.id).scalar() or 0

    # Total winnings
    total_winnings = db.query(func.sum(Bet.potential_payout))\
        .filter(Bet.user_id == current_user.id)\
        .filter(Bet.status == BetStatus.WON).scalar() or 0

    # Win rate
    win_rate = (won_bets / (won_bets + lost_bets)) * 100 if (won_bets + lost_bets) > 0 else 0

    return {
        "total_bets": total_bets,
        "active_bets": active_bets,
        "won_bets": won_bets,
        "lost_bets": lost_bets,
        "total_wagered": total_wagered,
        "total_winnings": total_winnings,
        "win_rate": round(win_rate, 2),
        "current_balance": current_user.balance
    }

@router.get("/cashout-value/{bet_id}")
async def get_cashout_value(
    bet_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get the current cash-out value for a bet."""
    from sqlalchemy import and_

    bet = db.query(Bet).filter(
        and_(Bet.id == bet_id, Bet.user_id == current_user.id)
    ).first()

    if not bet:
        raise HTTPException(status_code=404, detail="Bet not found")

    from ..services.market_maker import MarketMaker
    market_maker = MarketMaker()
    cash_out_value = market_maker.calculate_cash_out_value(db, bet)

    return {
        "bet_id": bet_id,
        "cash_out_value": cash_out_value,
        "available": cash_out_value > 0
    }
